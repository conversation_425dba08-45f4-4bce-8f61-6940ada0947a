<?php
/**
 * Test status update functionality
 */

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Account;

echo "=== Test Status Update ===\n\n";

// Get the most recent account (likely imported)
$account = Account::orderBy('created_at', 'desc')->first();
if (!$account) {
    echo "No accounts found\n";
    exit;
}

echo "Testing account: ID {$account->id}, Name: {$account->name}\n";
echo "Current status: {$account->status} (" . ($account->status == 1 ? 'Active' : 'Locked') . ")\n";
echo "Current sync_with_school: " . ($account->sync_with_school ? 'true' : 'false') . "\n\n";

// Test toggling status
$originalStatus = $account->status;
$newStatus = $originalStatus == 1 ? 0 : 1;

echo "Attempting to change status from {$originalStatus} to {$newStatus}\n";

// Test direct update
$account->status = $newStatus;
$account->sync_with_school = false; // Ensure sync doesn't interfere
$saved = $account->save();

echo "Save result: " . ($saved ? 'SUCCESS' : 'FAILED') . "\n";

// Refresh from database
$account->refresh();
echo "Status after save: {$account->status} (" . ($account->status == 1 ? 'Active' : 'Locked') . ")\n";

if ($account->status == $newStatus) {
    echo "✓ Status update WORKED\n";
    
    // Restore original status
    $account->status = $originalStatus;
    $account->save();
    echo "Restored original status\n";
} else {
    echo "✗ Status update FAILED\n";
    echo "Expected: {$newStatus}, Got: {$account->status}\n";
}

echo "\n=== Test Complete ===\n";
