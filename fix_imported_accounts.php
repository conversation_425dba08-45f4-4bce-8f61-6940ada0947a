<?php
/**
 * Fix existing imported accounts that have sync_with_school set to true
 */

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Account;

echo "=== Fixing Imported Accounts ===\n\n";

// Find accounts that were likely imported (have school_id and sync_with_school = true)
// and were created recently (today or yesterday)
$recentDate = now()->subDays(2);
$importedAccounts = Account::where('sync_with_school', true)
    ->whereNotNull('school_id')
    ->where('created_at', '>=', $recentDate)
    ->get();

echo "Found " . $importedAccounts->count() . " recently imported accounts with sync_with_school = true\n\n";

if ($importedAccounts->count() > 0) {
    echo "Accounts to be updated:\n";
    foreach ($importedAccounts as $account) {
        echo "  ID: {$account->id}, Name: {$account->name}, Created: {$account->created_at}\n";
    }
    
    echo "\nUpdating accounts to set sync_with_school = false...\n";
    
    $updated = 0;
    foreach ($importedAccounts as $account) {
        $account->sync_with_school = false;
        $account->save();
        $updated++;
    }
    
    echo "Successfully updated {$updated} accounts.\n";
    echo "These accounts should now be editable.\n";
} else {
    echo "No recently imported accounts found that need updating.\n";
}

echo "\n=== Fix Complete ===\n";
