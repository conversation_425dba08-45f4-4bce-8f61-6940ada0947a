<?php

namespace App\Http\Requests\Account;

use App\Http\Helpers;
use App\Models\Account;
use App\Models\AccountType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $provinces = [];
        $districts = [];

        try {
            $provinces = Helpers::getProvinces();
            $districts = Helpers::getDistrictsByProvinceId($this->province);
        } catch (\Exception $e){

        }

        return [
            'name' => 'required',
            'name_org' => 'required',
            'phone_number' => [
                'required',
                Rule::unique('accounts', 'phone_number')->where(function ($query)  {
                    return $query->where('deleted_at', null)->where('id', '<>', $this->route('id'));
                })
            ],
            'email' => [
                'required',
                'email',
                Rule::unique('accounts', 'email')->where(function ($query)  {
                    return $query->where('deleted_at', null)->where('id', '<>', $this->route('id'));
                })
            ],
            'status' => Rule::in(Account::STATUS_ACTIVE, Account::STATUS_LOCK),
            'province' => [
                function ($attr, $value, $fail) use ($provinces) {
                    if(!array_key_exists($value, $provinces)){
                        $fail('Attribute ' . $attr . ' not found');
                    }
                }
            ],
            'district' => [
                function ($attr, $value, $fail) use ($districts) {
                    if(!array_key_exists($value, $districts)){
                        $fail('Attribute ' . $attr . ' not found');
                    }
                }
            ],
            'mamnon_account_type' => [
                function ($attr, $value, $fail) {
                    if(!AccountType::query()->where('id', $value)->exists()){
                        $fail('Attribute ' . $attr . ' not found');
                    }
                }
            ],
            'tieuhoc_account_type' => [
                function ($attr, $value, $fail) {
                    if(!AccountType::query()->where('id', $value)->exists()){
                        $fail('Attribute ' . $attr . ' not found');
                    }
                }
            ]
        ];
    }
}
