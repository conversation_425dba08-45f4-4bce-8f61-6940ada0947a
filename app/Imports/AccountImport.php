<?php

namespace App\Imports;

use App\Http\Constants;
use App\Http\Helpers;
use App\Models\Account;
use App\Models\AccountType;
use App\Models\Config;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Nette\Utils\DateTime;

class AccountImport implements ToCollection, WithHeadingRow, WithChunkReading
{
    private $logicErrors;
    private $count;
    private $totalCount;
    private $mapProvinces;
    private $mapDistricts;
    private $defaultextendexpired;

    /**
     * @throws Exception
     */
    public function __construct()
    {
        $provinces = Helpers::getProvinces();
        $districts = Helpers::getDistricts();

        foreach ($provinces as $key => $province){
            $this->mapProvinces[$province['name']] = $province;
            $this->mapProvinces[$key] = $province;
        }

        foreach ($districts as $key => $district){
            $this->mapDistricts[$district['name']] = $district;
            $this->mapDistricts[$key] = $district;
        }

        $this->logicErrors = null;
        $this->count = 0;
        $config = Config::query()->first();
        $this->defaultextendexpired = $config->defaultextendexpired;
    }

    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        DB::beginTransaction();

        $this->totalCount = count($collection);
        if($this->defaultextendexpired != null){
            $dateExpired = now()->addDays($this->defaultextendexpired);
        } else {
            $dateExpired = null;
        }

        foreach ($collection as $row) {
            if($row == null){
                continue;
            }

            // init value
            $values = array_values($row->toArray());
            $name = $values[0];
            $nameOrg = $values[1];
            $phoneNumber = $values[2];

            $email = $values[3];

            // Handle duplicate accounts with comprehensive logic
            $duplicateHandled = $this->handleDuplicateAccounts($phoneNumber, $email, $name, $nameOrg, $values);
            if ($duplicateHandled === 'skip') {
                $this->count++;
                continue; // Skip this row and continue to next
            } elseif ($duplicateHandled === 'error') {
                return; // Error occurred, stop processing
            }

            $provinceName = $values[4];
            $districtName = $values[5];
            $class = $values[6];
            $status = $values[7];
            $isMamnon = $values[8];
            $isTieuhoc = $values[9];

            // check status
            // Only allow active and locked status (pending status is removed)
            if(!in_array($status, [Account::STATUS_ACTIVE, Account::STATUS_LOCK])){
                $this->count++;
                $this->logicErrors = "Mã trạng thái không đúng ở dòng " . $this->count;
                DB::rollBack();
                return;
            }

            // map name to district and province
            $district = null;
            $province = null;
            if(array_key_exists($provinceName, $this->mapProvinces)){
                $province = $this->mapProvinces[$provinceName];
            }

            if(array_key_exists($districtName, $this->mapDistricts)){
                $district = $this->mapDistricts[$districtName];
            }

            // map account
            if($isMamnon != null){
                $isMamnon = 1;
            } else {
                $isMamnon = 0;
            }

            if($isTieuhoc != null){
                $isTieuhoc = 1;
            } else {
                $isMamnon = 0;
            }

            // map account type
            $mamnonAccountType = null;
            $tieuhocAccountType = null;
            if($isMamnon == 1){
                $mamnonAccountType = AccountType::query()->where('code', $values[10])->first();
                if($mamnonAccountType == null){
                    $this->count++;
                    $this->logicErrors = "Mã loại tài khoản mầm non không đúng ở dòng " . $this->count;
                    DB::rollBack();
                    return;
                }
            }

            if($isTieuhoc == 1){
                $tieuhocAccountType = AccountType::query()->where('code', $values[11])->first();
                if($tieuhocAccountType == null){
                    $this->count++;
                    $this->logicErrors = "Mã loại tài khoản tiểu học không đúng ở dòng " . $this->count;
                    DB::rollBack();
                    return;
                }
            }

            // time expired
            $dateExpiredExcel = $values[12];
            if($dateExpiredExcel != null){
                try {
                    $dateExpired = DateTime::createFromFormat(Constants::FORMAT_DATE_USER, $dateExpiredExcel);
                    if(!$dateExpired){
                        $this->count++;
                        $this->logicErrors = "Định dạng ngày hết hạn không đúng ở dòng " . $this->count;
                        DB::rollBack();
                        return;
                    }
                } catch (\Exception $e){
                    $this->count++;
                    $this->logicErrors = "Định dạng ngày hết hạn không đúng ở dòng " . $this->count;
                    DB::rollBack();
                    return;
                }
            }

            // --- SCHOOL CREATION/ASSOCIATION LOGIC ---
            $school = null;
            if ($nameOrg) {
                $school = \App\Models\School::where('name', $nameOrg)
                    ->where('province', $province != null ? $province['code'] : null)
                    ->where('district', $district != null ? $district['code'] : null)
                    ->first();
                if (!$school) {
                    $school = \App\Models\School::create([
                        'name' => $nameOrg,
                        'province' => $province != null ? $province['code'] : null,
                        'district' => $district != null ? $district['code'] : null,
                        'is_mamnon' => $isMamnon,
                        'is_tieuhoc' => $isTieuhoc,
                        'mamnon_account_type_id' => $mamnonAccountType != null ? $mamnonAccountType->id : null,
                        'tieuhoc_account_type_id' => $tieuhocAccountType != null ? $tieuhocAccountType->id : null,
                        'time_expired' => $dateExpired,
                    ]);
                }
            }

            $account = new Account();
            $account->fill([
                'name' => $name,
                'name_org' => $nameOrg,
                'email' => $email,
                'phone_number' => $phoneNumber,
                'password' => Hash::make($phoneNumber),
                'status' => $status,
                'class' => $class,
                'province' => $province != null? $province['code']: null,
                'district' => $district != null? $district['code']: null,
                'is_mamnon' => $isMamnon,
                'is_tieuhoc' => $isTieuhoc,
                'mamnon_account_type_id' => $mamnonAccountType != null? $mamnonAccountType->id : null,
                'tieuhoc_account_type_id' => $tieuhocAccountType != null? $tieuhocAccountType->id : null,
                'time_expired' => $dateExpired,
                'school_id' => $school ? $school->id : null,
                'phone_number_as_password' => 1, // Set phone number as password by default
                'sync_with_school' => false, // Allow editing of imported accounts by default
            ]);
            $account->save();
            $this->count++;
        }

        DB::commit();
    }

    public function getLogicErrors()
    {
        return $this->logicErrors;
    }

    public function importedCount()
    {
        return $this->count;
    }

    public function totalCount()
    {
        return $this->totalCount;
    }

    public function chunkSize(): int
    {
        return 100;
    }

    /**
     * Handle duplicate accounts with comprehensive logic
     * Returns: 'continue' = proceed with creating new account, 'skip' = skip this row, 'error' = stop processing
     */
    private function handleDuplicateAccounts($phoneNumber, $email, $name, $nameOrg, $values)
    {
        // Find existing accounts by phone and email
        $phoneAccount = Account::withTrashed()->where('phone_number', $phoneNumber)->first();
        $emailAccount = Account::withTrashed()->where('email', $email)->first();

        // Handle soft-deleted accounts first
        if ($phoneAccount && $phoneAccount->deleted_at !== null) {
            $phoneAccount->forceDelete();
            $phoneAccount = null;
        }
        if ($emailAccount && $emailAccount->deleted_at !== null) {
            $emailAccount->forceDelete();
            $emailAccount = null;
        }

        // Case 1: No duplicates found
        if (!$phoneAccount && !$emailAccount) {
            return 'continue';
        }

        // Case 2: Same account (both phone and email match the same account)
        if ($phoneAccount && $emailAccount && $phoneAccount->id === $emailAccount->id) {
            return $this->handleSameAccountDuplicate($phoneAccount, $name, $nameOrg, $values);
        }

        // Case 3: Different accounts (phone matches one, email matches another)
        if ($phoneAccount && $emailAccount && $phoneAccount->id !== $emailAccount->id) {
            return $this->handleDifferentAccountsDuplicate($phoneAccount, $emailAccount, $name, $nameOrg, $values);
        }

        // Case 4: Only phone number duplicate
        if ($phoneAccount && !$emailAccount) {
            return $this->handlePhoneOnlyDuplicate($phoneAccount, $name, $nameOrg, $values);
        }

        // Case 5: Only email duplicate
        if (!$phoneAccount && $emailAccount) {
            return $this->handleEmailOnlyDuplicate($emailAccount, $name, $nameOrg, $values);
        }

        return 'continue';
    }

    /**
     * Handle case where both phone and email match the same existing account
     */
    private function handleSameAccountDuplicate($existingAccount, $name, $nameOrg, $values)
    {
        // Check if all fields are the same
        if ($this->areAllFieldsSame($existingAccount, $name, $nameOrg, $values)) {
            if ($existingAccount->status != Account::STATUS_ACTIVE) {
                // Activate the account if it's not active
                $existingAccount->status = Account::STATUS_ACTIVE;
                $existingAccount->save();
            }
            // Skip this row (account already exists and is now active)
            return 'skip';
        } else {
            // Some fields are different, delete old and create new
            $existingAccount->forceDelete();
            return 'continue';
        }
    }

    /**
     * Handle case where phone matches one account and email matches another
     */
    private function handleDifferentAccountsDuplicate($phoneAccount, $emailAccount, $name, $nameOrg, $values)
    {
        // Prioritize email address - delete the email account, keep phone account
        $emailAccount->forceDelete();

        // Now handle the phone account
        return $this->handlePhoneOnlyDuplicate($phoneAccount, $name, $nameOrg, $values);
    }

    /**
     * Handle case where only phone number is duplicate
     */
    private function handlePhoneOnlyDuplicate($phoneAccount, $name, $nameOrg, $values)
    {
        // Since phone_number_as_password is enabled by default, phone cannot be duplicated if account is active
        if ($phoneAccount->status == Account::STATUS_ACTIVE) {
            $this->count++;
            $this->logicErrors = "Số điện thoại đã được sử dụng bởi tài khoản đang hoạt động ở dòng " . $this->count;
            DB::rollBack();
            return 'error';
        } else {
            // Account is not active, delete and create new
            $phoneAccount->forceDelete();
            return 'continue';
        }
    }

    /**
     * Handle case where only email is duplicate
     */
    private function handleEmailOnlyDuplicate($emailAccount, $name, $nameOrg, $values)
    {
        // Check if all fields are the same
        if ($this->areAllFieldsSame($emailAccount, $name, $nameOrg, $values)) {
            if ($emailAccount->status != Account::STATUS_ACTIVE) {
                // Activate the account if it's not active
                $emailAccount->status = Account::STATUS_ACTIVE;
                $emailAccount->save();
            }
            // Skip this row (account already exists and is now active)
            return 'skip';
        } else {
            // Some fields are different, delete old and create new
            $emailAccount->forceDelete();
            return 'continue';
        }
    }

    /**
     * Check if all important fields are the same between existing account and CSV data
     */
    private function areAllFieldsSame($existingAccount, $name, $nameOrg, $values)
    {
        $provinceName = $values[4];
        $districtName = $values[5];
        $class = $values[6];
        $status = $values[7];
        $isMamnon = $values[8];
        $isTieuhoc = $values[9];

        // Map province and district names to codes for comparison
        $province = null;
        $district = null;
        if(array_key_exists($provinceName, $this->mapProvinces)){
            $province = $this->mapProvinces[$provinceName];
        }
        if(array_key_exists($districtName, $this->mapDistricts)){
            $district = $this->mapDistricts[$districtName];
        }

        // Compare all important fields
        return $existingAccount->name === $name &&
               $existingAccount->name_org === $nameOrg &&
               $existingAccount->class === $class &&
               $existingAccount->status == $status &&
               $existingAccount->is_mamnon == $isMamnon &&
               $existingAccount->is_tieuhoc == $isTieuhoc &&
               $existingAccount->province === ($province ? $province['code'] : null) &&
               $existingAccount->district === ($district ? $district['code'] : null);
    }
}
