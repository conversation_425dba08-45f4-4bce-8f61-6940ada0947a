<?php
/**
 * Test account update functionality
 */

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Account;

echo "=== Test Account Update ===\n\n";

// Get the most recent account (likely imported)
$account = Account::orderBy('created_at', 'desc')->first();
if (!$account) {
    echo "No accounts found\n";
    exit;
}

echo "Testing account: ID {$account->id}, Name: {$account->name}\n";
echo "Current email: {$account->email}\n";
echo "Current phone: {$account->phone_number}\n";
echo "Current sync_with_school: " . ($account->sync_with_school ? 'true' : 'false') . "\n\n";

// Try to update the account directly
$originalName = $account->name;
$testName = $originalName . " (UPDATED)";

echo "Attempting to update name from '{$originalName}' to '{$testName}'\n";

$account->name = $testName;
$account->sync_with_school = false; // Ensure sync is disabled
$saved = $account->save();

echo "Save result: " . ($saved ? 'SUCCESS' : 'FAILED') . "\n";

// Refresh from database
$account->refresh();
echo "Name after save: {$account->name}\n";
echo "Sync with school after save: " . ($account->sync_with_school ? 'true' : 'false') . "\n";

if ($account->name === $testName) {
    echo "✓ Direct update WORKED - the issue is likely in the controller logic\n";
    
    // Restore original name
    $account->name = $originalName;
    $account->save();
    echo "Restored original name\n";
} else {
    echo "✗ Direct update FAILED - there might be a database constraint issue\n";
}

echo "\n=== Test Complete ===\n";
