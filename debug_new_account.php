<?php
/**
 * Debug script for new account editing issues
 */

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Account;
use App\Models\User;

echo "=== Debug New Account Editing ===\n\n";

// Get the most recent account (likely imported)
$newAccount = Account::orderBy('created_at', 'desc')->first();
if (!$newAccount) {
    echo "No accounts found\n";
    exit;
}

echo "Testing newest account:\n";
echo "ID: {$newAccount->id}\n";
echo "Name: {$newAccount->name}\n";
echo "Email: {$newAccount->email}\n";
echo "Phone: {$newAccount->phone_number}\n";
echo "Created: {$newAccount->created_at}\n";
echo "Updated: {$newAccount->updated_at}\n";
echo "Phone as Password: " . ($newAccount->phone_number_as_password ?? 'NULL') . "\n";
echo "Sync with School: " . ($newAccount->sync_with_school ? 'YES' : 'NO') . "\n";
echo "School ID: " . ($newAccount->school_id ?? 'NULL') . "\n";
echo "\n";

// Check if user has permission
$user = User::first();
if ($user) {
    echo "Current user: {$user->name}\n";
    
    // Check permission
    $canEdit = $user->can('edit-account', App\Models\Account::class);
    echo "Can edit accounts: " . ($canEdit ? 'YES' : 'NO') . "\n";
    
    if (!$canEdit) {
        echo "ERROR: User does not have edit-account permission!\n";
        echo "User roles: ";
        foreach ($user->roles as $role) {
            echo $role->name . " ";
        }
        echo "\n";
    }
} else {
    echo "No users found\n";
}

echo "\n";

// Check account attributes that might affect editing
echo "Account attributes that might affect editing:\n";
$attributes = $newAccount->getAttributes();
foreach ($attributes as $key => $value) {
    if (in_array($key, ['sync_with_school', 'phone_number_as_password', 'school_id', 'status'])) {
        echo "  {$key}: " . ($value ?? 'NULL') . "\n";
    }
}

echo "\n=== Debug Complete ===\n";
