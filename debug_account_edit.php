<?php
/**
 * Debug script to check account editing issues
 * Run this from the Laravel root directory: php debug_account_edit.php
 */

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Account;
use App\Models\User;
use App\Models\Permission;
use App\Models\Role;
use Illuminate\Support\Facades\Auth;

echo "=== Account Edit Debug Script ===\n\n";

// Check if there are any imported accounts
echo "1. Checking imported accounts...\n";
$totalAccounts = Account::count();
$recentAccounts = Account::orderBy('created_at', 'desc')->take(10)->get();

echo "Total accounts: $totalAccounts\n";
echo "Recent accounts (last 10):\n";
foreach ($recentAccounts as $account) {
    echo "  ID: {$account->id}, Name: {$account->name}, Email: {$account->email}, Created: {$account->created_at}\n";
}

// Check permissions
echo "\n2. Checking permissions...\n";
$editPermission = Permission::where('name', 'edit-account')->first();
if ($editPermission) {
    echo "Edit-account permission exists (ID: {$editPermission->id})\n";
    $rolesWithPermission = $editPermission->roles;
    echo "Roles with edit-account permission:\n";
    foreach ($rolesWithPermission as $role) {
        echo "  - {$role->name} (ID: {$role->id})\n";
    }
} else {
    echo "ERROR: edit-account permission not found!\n";
}

// Check users and their roles
echo "\n3. Checking users and roles...\n";
$users = User::with('roles')->get();
foreach ($users as $user) {
    echo "User: {$user->name} (ID: {$user->id})\n";
    echo "  Roles: ";
    if ($user->roles->count() > 0) {
        echo $user->roles->pluck('name')->implode(', ') . "\n";
        
        // Check if user has edit-account permission
        if ($editPermission) {
            $hasPermission = $user->hasPermission($editPermission);
            echo "  Has edit-account permission: " . ($hasPermission ? 'YES' : 'NO') . "\n";
        }
    } else {
        echo "No roles assigned\n";
    }
    echo "\n";
}

// Check account validation rules
echo "4. Checking account validation...\n";
echo "Account fillable fields:\n";
$account = new Account();
$fillable = $account->getFillable();
foreach ($fillable as $field) {
    echo "  - $field\n";
}

// Check for any specific account that might have issues
echo "\n5. Sample account check...\n";
$sampleAccount = Account::first();
if ($sampleAccount) {
    echo "Sample account details:\n";
    echo "  ID: {$sampleAccount->id}\n";
    echo "  Name: {$sampleAccount->name}\n";
    echo "  Email: {$sampleAccount->email}\n";
    echo "  Status: {$sampleAccount->status}\n";
    echo "  Province: {$sampleAccount->province}\n";
    echo "  District: {$sampleAccount->district}\n";
    echo "  Is Mamnon: {$sampleAccount->is_mamnon}\n";
    echo "  Is Tieuhoc: {$sampleAccount->is_tieuhoc}\n";
    echo "  Mamnon Account Type ID: {$sampleAccount->mamnon_account_type_id}\n";
    echo "  Tieuhoc Account Type ID: {$sampleAccount->tieuhoc_account_type_id}\n";
    echo "  School ID: {$sampleAccount->school_id}\n";
    echo "  Sync with School: " . ($sampleAccount->sync_with_school ? 'YES' : 'NO') . "\n";
    echo "  Phone as Password: " . ($sampleAccount->phone_number_as_password ? 'YES' : 'NO') . "\n";
    echo "  Created: {$sampleAccount->created_at}\n";
    echo "  Updated: {$sampleAccount->updated_at}\n";
}

echo "\n=== Debug Complete ===\n";
echo "Please run this script and share the output to help diagnose the issue.\n";
